"""
MuMu自动化脚本示例
演示如何使用AutomationController进行自动化操作
"""

from automation_controller import AutomationController
import time

def example_script():
    """示例自动化脚本"""
    
    # 创建自动化控制器
    controller = AutomationController()
    
    print("MuMu自动化脚本示例")
    print("==================")
    
    # 示例1: 简单的坐标点击序列
    print("\n示例1: 坐标点击序列")
    controller.clear_script()
    
    # 添加动作
    controller.click_coordinate(100, 100, delay=1)  # 点击坐标(100,100)，延时1秒
    controller.wait_delay(2)                        # 等待2秒
    controller.click_coordinate(200, 200, delay=1)  # 点击坐标(200,200)，延时1秒
    
    print(f"脚本包含 {len(controller.current_script)} 个动作")
    
    # 示例2: 图像匹配点击
    print("\n示例2: 图像匹配点击")
    controller.clear_script()
    
    # 注意: 需要先准备模板图片文件
    # controller.click_image("template.png", threshold=0.8, delay=1)
    # controller.wait_for_image("target.png", timeout=10)
    # controller.click_image("button.png", threshold=0.9)
    
    print("图像匹配示例（需要模板图片文件）")
    
    # 示例3: 循环操作
    print("\n示例3: 循环操作")
    controller.clear_script()
    
    controller.loop_start(3)                        # 开始循环3次
    controller.click_coordinate(150, 150, delay=0.5)
    controller.wait_delay(1)
    controller.click_coordinate(250, 250, delay=0.5)
    controller.wait_delay(1)
    controller.loop_end()                           # 结束循环
    
    print(f"循环脚本包含 {len(controller.current_script)} 个动作")
    
    # 执行脚本示例
    print("\n是否要执行示例脚本？(y/n): ", end="")
    choice = input().lower()
    
    if choice == 'y':
        print("开始执行脚本...")
        
        def script_callback(event_type, message):
            print(f"[{event_type.upper()}] {message}")
        
        # 执行脚本
        if controller.execute_script(script_callback):
            print("脚本开始执行...")
            
            # 等待脚本完成
            while controller.is_running:
                time.sleep(0.1)
            
            print("脚本执行完成！")
        else:
            print("脚本执行失败（可能已有脚本在运行）")
    
    # 保存脚本示例
    print("\n保存脚本示例")
    if controller.save_script("example_automation.json"):
        print("脚本已保存到 example_automation.json")
    else:
        print("脚本保存失败")

def interactive_mode():
    """交互模式 - 让用户手动创建脚本"""
    
    controller = AutomationController()
    controller.clear_script()
    
    print("\n交互模式 - 创建自定义脚本")
    print("============================")
    print("可用命令:")
    print("1. click <x> <y> - 添加坐标点击")
    print("2. wait <seconds> - 添加延时等待")
    print("3. loop <count> - 开始循环")
    print("4. endloop - 结束循环")
    print("5. show - 显示当前脚本")
    print("6. run - 执行脚本")
    print("7. save <filename> - 保存脚本")
    print("8. load <filename> - 加载脚本")
    print("9. clear - 清空脚本")
    print("10. quit - 退出")
    
    while True:
        try:
            command = input("\n> ").strip().split()
            if not command:
                continue
            
            cmd = command[0].lower()
            
            if cmd == "click" and len(command) >= 3:
                x, y = int(command[1]), int(command[2])
                controller.click_coordinate(x, y)
                print(f"添加点击动作: ({x}, {y})")
            
            elif cmd == "wait" and len(command) >= 2:
                seconds = float(command[1])
                controller.wait_delay(seconds)
                print(f"添加等待动作: {seconds}秒")
            
            elif cmd == "loop" and len(command) >= 2:
                count = int(command[1])
                controller.loop_start(count)
                print(f"添加循环开始: {count}次")
            
            elif cmd == "endloop":
                controller.loop_end()
                print("添加循环结束")
            
            elif cmd == "show":
                info = controller.get_script_info()
                print(f"当前脚本包含 {info['total_actions']} 个动作:")
                for i, action in enumerate(info['actions']):
                    print(f"  {i+1}. {action['type']} - {action['params']}")
            
            elif cmd == "run":
                if controller.current_script:
                    print("开始执行脚本...")
                    
                    def callback(event_type, message):
                        print(f"[{event_type.upper()}] {message}")
                    
                    if controller.execute_script(callback):
                        while controller.is_running:
                            time.sleep(0.1)
                        print("脚本执行完成！")
                    else:
                        print("脚本执行失败")
                else:
                    print("脚本为空，请先添加动作")
            
            elif cmd == "save" and len(command) >= 2:
                filename = command[1]
                if controller.save_script(filename):
                    print(f"脚本已保存到 {filename}")
                else:
                    print("保存失败")
            
            elif cmd == "load" and len(command) >= 2:
                filename = command[1]
                result = controller.load_script(filename)
                if result == True:
                    print(f"脚本已从 {filename} 加载")
                else:
                    print(f"加载失败: {result[1] if isinstance(result, tuple) else '未知错误'}")
            
            elif cmd == "clear":
                if controller.clear_script():
                    print("脚本已清空")
                else:
                    print("无法清空脚本（可能正在执行）")
            
            elif cmd == "quit":
                break
            
            else:
                print("无效命令，请查看帮助")
        
        except ValueError:
            print("参数错误，请检查输入")
        except KeyboardInterrupt:
            print("\n退出交互模式")
            break
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    print("MuMu自动化脚本示例程序")
    print("请确保MuMuNxDevice.exe程序正在运行")
    print()
    
    while True:
        print("选择模式:")
        print("1. 运行示例脚本")
        print("2. 交互模式")
        print("3. 退出")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            example_script()
        elif choice == "2":
            interactive_mode()
        elif choice == "3":
            break
        else:
            print("无效选择，请重新输入")
