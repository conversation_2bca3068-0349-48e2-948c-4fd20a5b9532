"""
主用户界面模块
MuMu模拟器自动化工具的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from window_manager import MuMuWindowManager
from capture_tool import CaptureTool
from image_matcher import ImageMatcher


class MuMuAutomationGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MuMu模拟器自动化工具 v1.0")
        self.root.geometry("800x600")
        
        # 初始化组件
        self.window_manager = MuMuWindowManager()
        self.capture_tool = CaptureTool(self.root)
        self.image_matcher = ImageMatcher()
        
        # 状态变量
        self.is_running = False
        self.current_template = None
        
        self.create_widgets()
        self.update_status()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主菜单
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载模板图片", command=self.load_template)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="抓点抓图", command=self.open_capture_tool)
        tools_menu.add_command(label="刷新MuMu窗口", command=self.refresh_mumu_window)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.root, relief=tk.RAISED, bd=1)
        toolbar.pack(side=tk.TOP, fill=tk.X)
        
        # 抓点抓图按钮
        tk.Button(toolbar, text="抓点抓图", command=self.open_capture_tool).pack(side=tk.LEFT, padx=2, pady=2)
        
        # 加载模板按钮
        tk.Button(toolbar, text="加载模板", command=self.load_template).pack(side=tk.LEFT, padx=2, pady=2)
        
        # 查找图片按钮
        tk.Button(toolbar, text="查找图片", command=self.find_template).pack(side=tk.LEFT, padx=2, pady=2)
        
        # 点击图片按钮
        tk.Button(toolbar, text="点击图片", command=self.click_template).pack(side=tk.LEFT, padx=2, pady=2)
        
        # 分隔符
        tk.Frame(toolbar, width=2, bg="gray").pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 刷新窗口按钮
        tk.Button(toolbar, text="刷新MuMu", command=self.refresh_mumu_window).pack(side=tk.LEFT, padx=2, pady=2)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 窗口信息标签页
        self.create_window_info_tab(notebook)
        
        # 图像匹配标签页
        self.create_image_match_tab(notebook)
        
        # 自动化操作标签页
        self.create_automation_tab(notebook)
        
        # 日志标签页
        self.create_log_tab(notebook)
    
    def create_window_info_tab(self, parent):
        """创建窗口信息标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="窗口信息")
        
        # MuMu窗口信息
        info_frame = tk.LabelFrame(frame, text="MuMu窗口信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.window_info_text = scrolledtext.ScrolledText(info_frame, height=8, state=tk.DISABLED)
        self.window_info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 控制按钮
        control_frame = tk.Frame(info_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(control_frame, text="刷新窗口信息", command=self.update_window_info).pack(side=tk.LEFT, padx=2)
        tk.Button(control_frame, text="截取窗口", command=self.take_screenshot).pack(side=tk.LEFT, padx=2)
    
    def create_image_match_tab(self, parent):
        """创建图像匹配标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="图像匹配")
        
        # 模板信息
        template_frame = tk.LabelFrame(frame, text="模板图片")
        template_frame.pack(fill=tk.X, padx=5, pady=5)
        
        template_control = tk.Frame(template_frame)
        template_control.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(template_control, text="加载模板", command=self.load_template).pack(side=tk.LEFT, padx=2)
        
        self.template_info_var = tk.StringVar()
        self.template_info_var.set("未加载模板图片")
        tk.Label(template_control, textvariable=self.template_info_var).pack(side=tk.LEFT, padx=10)
        
        # 匹配设置
        match_frame = tk.LabelFrame(frame, text="匹配设置")
        match_frame.pack(fill=tk.X, padx=5, pady=5)
        
        settings_frame = tk.Frame(match_frame)
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(settings_frame, text="匹配阈值:").pack(side=tk.LEFT)
        self.threshold_var = tk.DoubleVar(value=0.8)
        threshold_scale = tk.Scale(settings_frame, from_=0.1, to=1.0, resolution=0.01, 
                                 orient=tk.HORIZONTAL, variable=self.threshold_var)
        threshold_scale.pack(side=tk.LEFT, padx=5)
        
        # 匹配操作
        match_control = tk.Frame(match_frame)
        match_control.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(match_control, text="查找图片", command=self.find_template).pack(side=tk.LEFT, padx=2)
        tk.Button(match_control, text="查找所有", command=self.find_all_templates).pack(side=tk.LEFT, padx=2)
        tk.Button(match_control, text="点击图片", command=self.click_template).pack(side=tk.LEFT, padx=2)
        
        # 匹配结果
        result_frame = tk.LabelFrame(frame, text="匹配结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.match_result_text = scrolledtext.ScrolledText(result_frame, height=10, state=tk.DISABLED)
        self.match_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_automation_tab(self, parent):
        """创建自动化操作标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="自动化操作")
        
        # 坐标点击
        click_frame = tk.LabelFrame(frame, text="坐标点击")
        click_frame.pack(fill=tk.X, padx=5, pady=5)
        
        coord_frame = tk.Frame(click_frame)
        coord_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(coord_frame, text="X坐标:").pack(side=tk.LEFT)
        self.click_x_var = tk.IntVar()
        tk.Entry(coord_frame, textvariable=self.click_x_var, width=8).pack(side=tk.LEFT, padx=2)
        
        tk.Label(coord_frame, text="Y坐标:").pack(side=tk.LEFT, padx=(10, 0))
        self.click_y_var = tk.IntVar()
        tk.Entry(coord_frame, textvariable=self.click_y_var, width=8).pack(side=tk.LEFT, padx=2)
        
        tk.Button(coord_frame, text="左键点击", command=lambda: self.click_coordinate('left')).pack(side=tk.LEFT, padx=5)
        tk.Button(coord_frame, text="右键点击", command=lambda: self.click_coordinate('right')).pack(side=tk.LEFT, padx=2)
        
        # 延时设置
        delay_frame = tk.LabelFrame(frame, text="延时设置")
        delay_frame.pack(fill=tk.X, padx=5, pady=5)
        
        delay_control = tk.Frame(delay_frame)
        delay_control.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(delay_control, text="延时(秒):").pack(side=tk.LEFT)
        self.delay_var = tk.DoubleVar(value=1.0)
        tk.Entry(delay_control, textvariable=self.delay_var, width=8).pack(side=tk.LEFT, padx=2)
        tk.Button(delay_control, text="等待", command=self.wait_delay).pack(side=tk.LEFT, padx=5)
    
    def create_log_tab(self, parent):
        """创建日志标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="操作日志")
        
        # 日志显示
        self.log_text = scrolledtext.ScrolledText(frame, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志控制
        log_control = tk.Frame(frame)
        log_control.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(log_control, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=2)
        tk.Button(log_control, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=2)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def update_status(self):
        """更新状态"""
        if self.window_manager.find_mumu_window():
            self.status_var.set("MuMu窗口已连接")
        else:
            self.status_var.set("未找到MuMu窗口")

        # 每5秒更新一次状态
        self.root.after(5000, self.update_status)

    # 事件处理方法
    def open_capture_tool(self):
        """打开抓点抓图工具"""
        self.capture_tool.start_capture()
        self.log_message("打开抓点抓图工具")

    def load_template(self):
        """加载模板图片"""
        if self.image_matcher.load_template():
            template_info = self.image_matcher.get_template_info()
            if template_info:
                info_text = f"已加载: {template_info['path']} ({template_info['width']}x{template_info['height']})"
                self.template_info_var.set(info_text)
                self.log_message(f"加载模板图片: {template_info['path']}")

    def find_template(self):
        """查找模板图片"""
        if self.image_matcher.template_image is None:
            messagebox.showerror("错误", "请先加载模板图片！")
            return

        threshold = self.threshold_var.get()
        result = self.image_matcher.find_template_in_window(threshold)

        self.match_result_text.config(state=tk.NORMAL)
        self.match_result_text.delete(1.0, tk.END)

        if result and result['found']:
            result_text = f"""匹配成功！
置信度: {result['confidence']:.3f}
左上角: {result['top_left']}
右下角: {result['bottom_right']}
中心点: {result['center']}
尺寸: {result['width']}x{result['height']}"""
            self.match_result_text.insert(tk.END, result_text)
            self.log_message(f"找到模板图片，置信度: {result['confidence']:.3f}")
        else:
            result_text = f"未找到匹配的图片\n置信度: {result['confidence']:.3f}\n阈值: {threshold}"
            self.match_result_text.insert(tk.END, result_text)
            self.log_message(f"未找到模板图片，置信度: {result['confidence']:.3f}")

        self.match_result_text.config(state=tk.DISABLED)

    def find_all_templates(self):
        """查找所有匹配的模板"""
        if self.image_matcher.template_image is None:
            messagebox.showerror("错误", "请先加载模板图片！")
            return

        threshold = self.threshold_var.get()
        matches = self.image_matcher.find_all_matches(threshold)

        self.match_result_text.config(state=tk.NORMAL)
        self.match_result_text.delete(1.0, tk.END)

        if matches:
            result_text = f"找到 {len(matches)} 个匹配项:\n\n"
            for i, match in enumerate(matches[:10]):  # 最多显示10个
                result_text += f"匹配 {i+1}:\n"
                result_text += f"  置信度: {match['confidence']:.3f}\n"
                result_text += f"  中心点: {match['center']}\n"
                result_text += f"  区域: {match['top_left']} 到 {match['bottom_right']}\n\n"

            if len(matches) > 10:
                result_text += f"... 还有 {len(matches) - 10} 个匹配项"

            self.match_result_text.insert(tk.END, result_text)
            self.log_message(f"找到 {len(matches)} 个匹配项")
        else:
            self.match_result_text.insert(tk.END, f"未找到匹配的图片\n阈值: {threshold}")
            self.log_message("未找到任何匹配项")

        self.match_result_text.config(state=tk.DISABLED)

    def click_template(self):
        """点击模板图片"""
        if self.image_matcher.template_image is None:
            messagebox.showerror("错误", "请先加载模板图片！")
            return

        threshold = self.threshold_var.get()
        result = self.image_matcher.click_on_template(threshold)

        if result['success']:
            self.log_message(f"成功点击模板图片，位置: {result['position']}, 置信度: {result['confidence']:.3f}")
            messagebox.showinfo("成功", f"已点击模板图片\n位置: {result['position']}")
        else:
            self.log_message(f"点击失败: {result['error']}")
            messagebox.showerror("失败", result['error'])

    def click_coordinate(self, button='left'):
        """点击指定坐标"""
        x = self.click_x_var.get()
        y = self.click_y_var.get()

        if self.window_manager.click_at_position(x, y, button):
            self.log_message(f"{button}键点击坐标: ({x}, {y})")
        else:
            self.log_message(f"点击失败: ({x}, {y})")
            messagebox.showerror("错误", "点击失败，请检查MuMu窗口是否可用")

    def wait_delay(self):
        """等待指定时间"""
        delay = self.delay_var.get()
        self.log_message(f"等待 {delay} 秒...")

        def wait_thread():
            time.sleep(delay)
            self.log_message("等待完成")

        threading.Thread(target=wait_thread, daemon=True).start()

    def refresh_mumu_window(self):
        """刷新MuMu窗口信息"""
        if self.window_manager.find_mumu_window():
            self.log_message("MuMu窗口刷新成功")
            self.update_window_info()
        else:
            self.log_message("未找到MuMu窗口")
            messagebox.showerror("错误", "未找到MuMu模拟器窗口！")

    def update_window_info(self):
        """更新窗口信息显示"""
        info = self.window_manager.get_window_info()

        self.window_info_text.config(state=tk.NORMAL)
        self.window_info_text.delete(1.0, tk.END)

        if info:
            info_text = f"""窗口句柄: {info['handle']}
窗口标题: {info['title']}
窗口类名: {info['class_name']}
窗口位置: {info['rect']}
是否可见: {info['is_visible']}
是否最小化: {info['is_minimized']}"""
            self.window_info_text.insert(tk.END, info_text)
        else:
            self.window_info_text.insert(tk.END, "未找到MuMu窗口")

        self.window_info_text.config(state=tk.DISABLED)

    def take_screenshot(self):
        """截取窗口截图"""
        screenshot = self.window_manager.get_window_screenshot()
        if screenshot:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg")]
            )
            if filename:
                screenshot.save(filename)
                self.log_message(f"截图已保存: {filename}")
                messagebox.showinfo("成功", f"截图已保存到: {filename}")
        else:
            messagebox.showerror("错误", "无法获取窗口截图")

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_log(self):
        """保存日志"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log_message(f"日志已保存: {filename}")

    def show_help(self):
        """显示帮助信息"""
        help_text = """MuMu模拟器自动化工具使用说明:

1. 抓点抓图功能:
   - 点击"抓点抓图"按钮打开抓图工具
   - 在截图上点击获取坐标，拖拽选择区域
   - 可以保存截图和选中区域

2. 图像匹配功能:
   - 先加载模板图片
   - 调整匹配阈值(0.1-1.0)
   - 点击"查找图片"或"查找所有"进行匹配
   - 点击"点击图片"自动点击找到的位置

3. 自动化操作:
   - 输入坐标进行精确点击
   - 设置延时等待时间

注意: 请确保MuMuNxDevice.exe程序正在运行"""

        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """MuMu模拟器自动化工具 v1.0

专门针对MuMuNxDevice.exe程序的自动化工具
类似按键精灵的功能，支持抓点抓图和图像匹配

开发语言: Python
主要库: tkinter, opencv-python, pywin32, pillow

功能特点:
- 窗口后台操作
- 抓点抓图工具
- 图像模板匹配
- 自动化点击操作"""

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行主程序"""
        self.log_message("MuMu自动化工具启动")
        self.root.mainloop()


if __name__ == "__main__":
    app = MuMuAutomationGUI()
    app.run()
