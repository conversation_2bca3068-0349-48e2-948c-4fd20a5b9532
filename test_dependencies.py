"""
测试依赖库是否正确安装
"""

def test_dependencies():
    print("正在检查依赖库...")
    
    # 测试tkinter
    try:
        import tkinter
        print("✓ tkinter - OK")
    except ImportError:
        print("✗ tkinter - 未安装")
    
    # 测试pywin32
    try:
        import win32gui
        import win32con
        import win32api
        import win32ui
        print("✓ pywin32 - OK")
    except ImportError:
        print("✗ pywin32 - 未安装，请运行: pip install pywin32")
    
    # 测试opencv
    try:
        import cv2
        print("✓ opencv-python - OK")
    except ImportError:
        print("✗ opencv-python - 未安装，请运行: pip install opencv-python")
    
    # 测试pillow
    try:
        from PIL import Image, ImageTk
        print("✓ pillow - OK")
    except ImportError:
        print("✗ pillow - 未安装，请运行: pip install pillow")
    
    # 测试numpy
    try:
        import numpy
        print("✓ numpy - OK")
    except ImportError:
        print("✗ numpy - 未安装，请运行: pip install numpy")
    
    print("\n依赖检查完成！")
    print("如有未安装的库，请运行: pip install -r requirements.txt")

if __name__ == "__main__":
    test_dependencies()
    input("按回车键退出...")
