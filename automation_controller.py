"""
自动化操作控制器
实现更高级的自动化操作功能
"""

import time
import threading
from window_manager import MuMuWindowManager
from image_matcher import ImageMatcher


class AutomationController:
    def __init__(self):
        self.window_manager = MuMuWindowManager()
        self.image_matcher = ImageMatcher()
        self.is_running = False
        self.stop_flag = False
        self.current_script = []
        self.script_index = 0
        
    def add_action(self, action_type, **kwargs):
        """添加自动化动作"""
        action = {
            'type': action_type,
            'params': kwargs,
            'timestamp': time.time()
        }
        self.current_script.append(action)
        return len(self.current_script) - 1
    
    def click_coordinate(self, x, y, button='left', delay=0):
        """点击坐标动作"""
        return self.add_action('click_coordinate', x=x, y=y, button=button, delay=delay)
    
    def click_image(self, image_path, threshold=0.8, button='left', delay=0, timeout=10):
        """点击图片动作"""
        return self.add_action('click_image', 
                              image_path=image_path, 
                              threshold=threshold, 
                              button=button, 
                              delay=delay,
                              timeout=timeout)
    
    def wait_for_image(self, image_path, threshold=0.8, timeout=30, check_interval=1):
        """等待图片出现动作"""
        return self.add_action('wait_for_image',
                              image_path=image_path,
                              threshold=threshold,
                              timeout=timeout,
                              check_interval=check_interval)
    
    def wait_delay(self, seconds):
        """等待延时动作"""
        return self.add_action('wait_delay', seconds=seconds)
    
    def loop_start(self, count=1):
        """循环开始标记"""
        return self.add_action('loop_start', count=count)
    
    def loop_end(self):
        """循环结束标记"""
        return self.add_action('loop_end')
    
    def execute_script(self, callback=None):
        """执行脚本"""
        if self.is_running:
            return False
        
        self.is_running = True
        self.stop_flag = False
        self.script_index = 0
        
        def run_script():
            try:
                self._execute_script_internal(callback)
            except Exception as e:
                if callback:
                    callback('error', f"脚本执行错误: {str(e)}")
            finally:
                self.is_running = False
        
        thread = threading.Thread(target=run_script, daemon=True)
        thread.start()
        return True
    
    def _execute_script_internal(self, callback):
        """内部脚本执行逻辑"""
        loop_stack = []  # 循环栈
        
        while self.script_index < len(self.current_script) and not self.stop_flag:
            action = self.current_script[self.script_index]
            action_type = action['type']
            params = action['params']
            
            if callback:
                callback('action', f"执行动作 {self.script_index + 1}: {action_type}")
            
            try:
                if action_type == 'click_coordinate':
                    self._execute_click_coordinate(params, callback)
                
                elif action_type == 'click_image':
                    self._execute_click_image(params, callback)
                
                elif action_type == 'wait_for_image':
                    self._execute_wait_for_image(params, callback)
                
                elif action_type == 'wait_delay':
                    self._execute_wait_delay(params, callback)
                
                elif action_type == 'loop_start':
                    loop_info = {
                        'start_index': self.script_index,
                        'count': params['count'],
                        'current_iteration': 0
                    }
                    loop_stack.append(loop_info)
                
                elif action_type == 'loop_end':
                    if loop_stack:
                        loop_info = loop_stack[-1]
                        loop_info['current_iteration'] += 1
                        
                        if loop_info['current_iteration'] < loop_info['count']:
                            # 继续循环
                            self.script_index = loop_info['start_index']
                            continue
                        else:
                            # 循环结束
                            loop_stack.pop()
                
                self.script_index += 1
                
            except Exception as e:
                if callback:
                    callback('error', f"动作执行失败: {str(e)}")
                break
        
        if callback:
            if self.stop_flag:
                callback('stopped', "脚本执行已停止")
            else:
                callback('completed', "脚本执行完成")
    
    def _execute_click_coordinate(self, params, callback):
        """执行坐标点击"""
        x, y = params['x'], params['y']
        button = params.get('button', 'left')
        delay = params.get('delay', 0)
        
        if delay > 0:
            time.sleep(delay)
        
        success = self.window_manager.click_at_position(x, y, button)
        if not success:
            raise Exception(f"点击坐标 ({x}, {y}) 失败")
        
        if callback:
            callback('info', f"点击坐标: ({x}, {y})")
    
    def _execute_click_image(self, params, callback):
        """执行图片点击"""
        image_path = params['image_path']
        threshold = params.get('threshold', 0.8)
        button = params.get('button', 'left')
        delay = params.get('delay', 0)
        timeout = params.get('timeout', 10)
        
        if delay > 0:
            time.sleep(delay)
        
        # 加载模板图片
        if not self.image_matcher.load_template(image_path):
            raise Exception(f"无法加载模板图片: {image_path}")
        
        # 等待图片出现并点击
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.stop_flag:
                return
            
            result = self.image_matcher.click_on_template(threshold, button)
            if result['success']:
                if callback:
                    callback('info', f"点击图片成功: {image_path}")
                return
            
            time.sleep(0.5)  # 每0.5秒检查一次
        
        raise Exception(f"在 {timeout} 秒内未找到图片: {image_path}")
    
    def _execute_wait_for_image(self, params, callback):
        """执行等待图片出现"""
        image_path = params['image_path']
        threshold = params.get('threshold', 0.8)
        timeout = params.get('timeout', 30)
        check_interval = params.get('check_interval', 1)
        
        # 加载模板图片
        if not self.image_matcher.load_template(image_path):
            raise Exception(f"无法加载模板图片: {image_path}")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.stop_flag:
                return
            
            result = self.image_matcher.find_template_in_window(threshold)
            if result and result['found']:
                if callback:
                    callback('info', f"找到图片: {image_path}")
                return
            
            time.sleep(check_interval)
        
        raise Exception(f"在 {timeout} 秒内未找到图片: {image_path}")
    
    def _execute_wait_delay(self, params, callback):
        """执行延时等待"""
        seconds = params['seconds']
        
        if callback:
            callback('info', f"等待 {seconds} 秒...")
        
        # 分段等待，以便响应停止信号
        wait_time = 0
        while wait_time < seconds and not self.stop_flag:
            sleep_time = min(0.1, seconds - wait_time)
            time.sleep(sleep_time)
            wait_time += sleep_time
    
    def stop_script(self):
        """停止脚本执行"""
        self.stop_flag = True
    
    def clear_script(self):
        """清空脚本"""
        if not self.is_running:
            self.current_script.clear()
            self.script_index = 0
            return True
        return False
    
    def get_script_info(self):
        """获取脚本信息"""
        return {
            'total_actions': len(self.current_script),
            'current_index': self.script_index,
            'is_running': self.is_running,
            'actions': self.current_script
        }
    
    def save_script(self, filename):
        """保存脚本到文件"""
        import json
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.current_script, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            return False, str(e)
    
    def load_script(self, filename):
        """从文件加载脚本"""
        import json
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.current_script = json.load(f)
            self.script_index = 0
            return True
        except Exception as e:
            return False, str(e)
    
    def remove_action(self, index):
        """删除指定索引的动作"""
        if 0 <= index < len(self.current_script) and not self.is_running:
            self.current_script.pop(index)
            return True
        return False
    
    def insert_action(self, index, action_type, **kwargs):
        """在指定位置插入动作"""
        if 0 <= index <= len(self.current_script) and not self.is_running:
            action = {
                'type': action_type,
                'params': kwargs,
                'timestamp': time.time()
            }
            self.current_script.insert(index, action)
            return True
        return False
