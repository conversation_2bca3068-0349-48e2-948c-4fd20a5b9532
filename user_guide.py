"""
用户指南窗口
"""

import tkinter as tk
from tkinter import ttk, scrolledtext


class UserGuideWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
    
    def show_guide(self):
        """显示用户指南窗口"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("MuMu自动化工具 - 使用指南")
        self.window.geometry("700x500")
        self.window.resizable(True, True)
        
        # 创建笔记本控件
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 快速开始标签页
        self.create_quick_start_tab(notebook)
        
        # 抓点抓图标签页
        self.create_capture_tab(notebook)
        
        # 图像匹配标签页
        self.create_image_match_tab(notebook)
        
        # 常见问题标签页
        self.create_faq_tab(notebook)
        
        # 关闭按钮
        close_btn = tk.But<PERSON>(self.window, text="关闭", command=self.close_window)
        close_btn.pack(pady=5)
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.close_window)
    
    def create_quick_start_tab(self, parent):
        """创建快速开始标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="快速开始")
        
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        content = """
🚀 快速开始指南

1. 准备工作
   • 确保MuMuNxDevice.exe程序正在运行
   • 如果MuMu窗口被最小化，程序会自动恢复窗口

2. 基本操作流程
   ① 启动程序：运行 run.py
   ② 检查连接：查看状态栏是否显示"MuMu窗口已连接"
   ③ 选择功能：使用工具栏按钮或菜单选择需要的功能

3. 主要功能
   🎯 抓点抓图：获取坐标和截图
   🔍 图像匹配：自动找到并点击图片
   ⚙️ 自动化操作：批量执行操作序列

4. 注意事项
   • 首次使用建议先测试抓点抓图功能
   • 图像匹配需要清晰的模板图片
   • 建议在固定分辨率下使用以获得最佳效果
        """
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_capture_tab(self, parent):
        """创建抓点抓图标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="抓点抓图")
        
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        content = """
📸 抓点抓图功能详解

1. 启动抓图工具
   • 点击工具栏的"抓点抓图"按钮
   • 或使用菜单：工具 → 抓点抓图

2. 抓图窗口操作
   🖱️ 鼠标操作：
   • 单击：获取点击位置的坐标
   • 拖拽：选择矩形区域
   • 移动：实时显示鼠标坐标

   🔧 工具按钮：
   • 重新截图：刷新当前截图
   • 保存截图：保存完整截图
   • 保存选区：保存选中的区域
   • 获取坐标：显示选区的详细坐标信息

3. 坐标信息
   • 左上角坐标：选区的起始位置
   • 右下角坐标：选区的结束位置
   • 中心点坐标：选区的中心位置
   • 区域大小：选区的宽度和高度

4. 使用技巧
   • 选择区域时，红色框显示当前选区
   • 状态栏显示实时坐标信息
   • 可以多次选择不同区域
   • 支持滚动查看大图片
        """
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_image_match_tab(self, parent):
        """创建图像匹配标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="图像匹配")
        
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        content = """
🔍 图像匹配功能详解

1. 准备模板图片
   • 使用抓点抓图工具截取目标图片
   • 确保图片清晰、独特
   • 支持PNG、JPG、BMP等格式
   • 建议图片大小适中（不要太大或太小）

2. 加载模板
   • 点击"加载模板"按钮
   • 选择准备好的模板图片文件
   • 加载成功后会显示图片信息

3. 匹配设置
   🎚️ 匹配阈值：
   • 范围：0.1 - 1.0
   • 推荐值：0.8 - 0.9
   • 值越高要求匹配越精确
   • 值越低容错性越强

4. 匹配操作
   • 查找图片：找到第一个匹配位置
   • 查找所有：找到所有匹配位置
   • 点击图片：自动点击找到的位置

5. 匹配结果
   • 显示匹配的置信度
   • 显示匹配位置的坐标
   • 显示匹配区域的大小

6. 使用技巧
   • 如果匹配失败，尝试调整阈值
   • 确保模板图片在当前屏幕中可见
   • 避免使用过于简单或重复的图片作为模板
        """
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_faq_tab(self, parent):
        """创建常见问题标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="常见问题")
        
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        content = """
❓ 常见问题解答

Q1: 提示"未找到MuMu模拟器窗口"怎么办？
A1: • 确保MuMuNxDevice.exe程序正在运行
    • 检查窗口标题是否包含"MuMu"字样
    • 尝试点击"刷新MuMu"按钮

Q2: 截图功能不工作怎么办？
A2: • 检查MuMu窗口是否被最小化
    • 确保程序有足够的权限
    • 尝试以管理员身份运行程序

Q3: 图像匹配不准确怎么办？
A3: • 调整匹配阈值（建议0.8-0.9）
    • 使用更清晰、更独特的模板图片
    • 确保模板图片在当前屏幕中可见

Q4: 点击位置不准确怎么办？
A4: • 检查MuMu窗口是否移动过位置
    • 点击"刷新MuMu"按钮更新窗口信息
    • 重新截图获取最新的窗口内容

Q5: 程序运行缓慢怎么办？
A5: • 关闭不必要的其他程序
    • 使用较小的模板图片
    • 适当调整匹配阈值

Q6: 如何提高自动化脚本的稳定性？
A6: • 在操作之间添加适当的延时
    • 使用图像等待功能确保界面加载完成
    • 准备多个备用模板图片

Q7: 支持哪些图片格式？
A7: • PNG（推荐）
    • JPG/JPEG
    • BMP
    • 其他PIL支持的格式

Q8: 如何获取技术支持？
A8: • 查看程序日志文件（mumu_automation.log）
    • 检查错误信息的详细描述
    • 确保所有依赖库正确安装
        """
        
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def close_window(self):
        """关闭窗口"""
        if self.window:
            self.window.destroy()
            self.window = None


# 全局用户指南实例
user_guide = UserGuideWindow()
