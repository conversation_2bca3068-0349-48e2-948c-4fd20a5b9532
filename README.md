# MuMu模拟器自动化工具

一个专门针对MuMuNxDevice.exe程序的自动化工具，类似按键精灵的功能，支持抓点抓图和图像匹配。

## 功能特点

- **窗口后台操作**: 可以在MuMu程序不在前台时进行操作
- **抓点抓图工具**: 类似按键精灵的抓点抓图功能
- **图像模板匹配**: 使用OpenCV进行高精度图像匹配
- **自动化操作**: 支持鼠标点击、延时等待等操作
- **脚本录制**: 可以录制和回放自动化脚本

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖库
```bash
pip install -r requirements.txt
```

依赖库包括：
- `pywin32` - Windows API操作
- `opencv-python` - 图像处理和匹配
- `pillow` - 图像处理
- `numpy` - 数值计算

## 使用方法

### 1. 启动程序
```bash
python run.py
```

### 2. 基本操作

#### 抓点抓图功能
1. 确保MuMuNxDevice.exe程序正在运行
2. 点击工具栏的"抓点抓图"按钮
3. 在弹出的窗口中：
   - 点击获取坐标点
   - 拖拽选择区域
   - 保存截图或选中区域

#### 图像匹配功能
1. 点击"加载模板"按钮选择模板图片
2. 调整匹配阈值（推荐0.8-0.9）
3. 点击"查找图片"进行匹配
4. 点击"点击图片"自动点击找到的位置

#### 坐标点击功能
1. 在"自动化操作"标签页中输入X、Y坐标
2. 点击"左键点击"或"右键点击"

### 3. 高级功能

#### 脚本自动化
- 使用`automation_controller.py`模块可以编写复杂的自动化脚本
- 支持循环、条件判断、图像等待等功能

## 文件结构

```
├── run.py                    # 启动脚本
├── main_gui.py              # 主界面
├── window_manager.py        # 窗口管理模块
├── capture_tool.py          # 抓点抓图工具
├── image_matcher.py         # 图像匹配模块
├── automation_controller.py # 自动化控制器
├── requirements.txt         # 依赖库列表
└── README.md               # 说明文档
```

## 使用注意事项

1. **MuMu程序要求**: 必须确保MuMuNxDevice.exe程序正在运行
2. **权限要求**: 程序需要足够的权限来控制其他窗口
3. **分辨率**: 图像匹配功能对屏幕分辨率敏感，建议在固定分辨率下使用
4. **模板图片**: 模板图片应该清晰且具有独特性，避免误匹配

## 常见问题

### Q: 提示"未找到MuMu模拟器窗口"
A: 请确保MuMuNxDevice.exe程序正在运行，并且窗口标题包含"MuMu"字样。

### Q: 图像匹配不准确
A: 尝试调整匹配阈值，或者使用更清晰、更独特的模板图片。

### Q: 点击位置不准确
A: 检查MuMu窗口是否移动过位置，可以点击"刷新MuMu"按钮更新窗口信息。

### Q: 程序无法启动
A: 检查是否安装了所有依赖库，运行 `pip install -r requirements.txt`

## 开发说明

### 核心模块说明

1. **WindowManager**: 负责检测和控制MuMu窗口
2. **CaptureTool**: 实现抓点抓图功能
3. **ImageMatcher**: 实现图像模板匹配
4. **AutomationController**: 提供高级自动化脚本功能

### 扩展开发

如需添加新功能，可以：
1. 在对应模块中添加新方法
2. 在主界面中添加对应的UI控件
3. 在事件处理中调用新功能

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 实现基本的抓点抓图功能
- 实现图像模板匹配
- 实现自动化点击操作
