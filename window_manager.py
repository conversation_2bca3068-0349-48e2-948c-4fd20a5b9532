"""
MuMu模拟器窗口管理模块
用于检测和控制MuMuNxDevice.exe程序窗口
"""

import win32gui
import win32con
import win32api
import win32ui
from PIL import Image
import numpy as np


class MuMuWindowManager:
    def __init__(self):
        self.target_process = "MuMuNxDevice.exe"
        self.window_handle = None
        self.window_rect = None
        
    def find_mumu_window(self):
        """查找MuMu模拟器窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                
                # 检查是否是MuMu模拟器窗口
                if "MuMu" in window_text or "mumu" in window_text.lower():
                    windows.append((hwnd, window_text, class_name))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # 选择第一个找到的MuMu窗口
            self.window_handle = windows[0][0]
            self.update_window_rect()
            return True
        return False
    
    def update_window_rect(self):
        """更新窗口位置和大小信息"""
        if self.window_handle:
            self.window_rect = win32gui.GetWindowRect(self.window_handle)
            return True
        return False
    
    def get_window_screenshot(self):
        """获取MuMu窗口截图"""
        if not self.window_handle:
            if not self.find_mumu_window():
                return None
        
        # 更新窗口位置
        self.update_window_rect()
        
        left, top, right, bottom = self.window_rect
        width = right - left
        height = bottom - top
        
        # 获取窗口设备上下文
        hwndDC = win32gui.GetWindowDC(self.window_handle)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        # 创建位图对象
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)
        
        # 截图
        result = win32gui.PrintWindow(self.window_handle, saveDC.GetSafeHdc(), 3)
        
        if result == 1:
            # 获取位图数据
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            # 转换为PIL图像
            img = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.window_handle, hwndDC)
            
            return img
        else:
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.window_handle, hwndDC)
            return None
    
    def get_window_info(self):
        """获取窗口信息"""
        if not self.window_handle:
            if not self.find_mumu_window():
                return None
        
        window_text = win32gui.GetWindowText(self.window_handle)
        class_name = win32gui.GetClassName(self.window_handle)
        
        return {
            'handle': self.window_handle,
            'title': window_text,
            'class_name': class_name,
            'rect': self.window_rect,
            'is_visible': win32gui.IsWindowVisible(self.window_handle),
            'is_minimized': win32gui.IsIconic(self.window_handle)
        }
    
    def click_at_position(self, x, y, button='left'):
        """在指定位置点击"""
        if not self.window_handle:
            return False
        
        # 转换为窗口相对坐标
        if self.window_rect:
            abs_x = self.window_rect[0] + x
            abs_y = self.window_rect[1] + y
        else:
            abs_x, abs_y = x, y
        
        # 发送点击消息
        if button == 'left':
            win32api.SetCursorPos((abs_x, abs_y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, abs_x, abs_y, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, abs_x, abs_y, 0, 0)
        elif button == 'right':
            win32api.SetCursorPos((abs_x, abs_y))
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, abs_x, abs_y, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, abs_x, abs_y, 0, 0)
        
        return True
