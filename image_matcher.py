"""
图像识别和匹配模块
使用OpenCV实现图像模板匹配功能
"""

import cv2
import numpy as np
from PIL import Image
import tkinter as tk
from tkinter import messagebox, filedialog
from window_manager import MuMuWindowManager


class ImageMatcher:
    def __init__(self):
        self.window_manager = MuMuWindowManager()
        self.template_image = None
        self.template_path = None
        
    def load_template(self, image_path=None):
        """加载模板图片"""
        if not image_path:
            image_path = filedialog.askopenfilename(
                title="选择模板图片",
                filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp"), ("所有文件", "*.*")]
            )
        
        if image_path:
            try:
                # 使用PIL加载图片
                pil_image = Image.open(image_path)
                # 转换为OpenCV格式
                self.template_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                self.template_path = image_path
                return True
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片: {str(e)}")
                return False
        return False
    
    def find_template_in_window(self, threshold=0.8, method=cv2.TM_CCOEFF_NORMED):
        """在MuMu窗口中查找模板图片"""
        if self.template_image is None:
            messagebox.showerror("错误", "请先加载模板图片！")
            return None
        
        # 获取MuMu窗口截图
        if not self.window_manager.find_mumu_window():
            messagebox.showerror("错误", "未找到MuMu模拟器窗口！")
            return None
        
        screenshot = self.window_manager.get_window_screenshot()
        if not screenshot:
            messagebox.showerror("错误", "无法获取窗口截图！")
            return None
        
        # 转换截图为OpenCV格式
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # 执行模板匹配
        result = cv2.matchTemplate(screenshot_cv, self.template_image, method)
        
        # 获取匹配结果
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        # 根据匹配方法选择最佳匹配位置
        if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            best_match = min_loc
            confidence = 1 - min_val
        else:
            best_match = max_loc
            confidence = max_val
        
        # 检查匹配度是否满足阈值
        if confidence >= threshold:
            template_h, template_w = self.template_image.shape[:2]
            
            match_info = {
                'found': True,
                'confidence': confidence,
                'top_left': best_match,
                'bottom_right': (best_match[0] + template_w, best_match[1] + template_h),
                'center': (best_match[0] + template_w // 2, best_match[1] + template_h // 2),
                'width': template_w,
                'height': template_h
            }
            
            return match_info
        else:
            return {
                'found': False,
                'confidence': confidence,
                'threshold': threshold
            }
    
    def find_all_matches(self, threshold=0.8, method=cv2.TM_CCOEFF_NORMED):
        """查找所有匹配的位置"""
        if self.template_image is None:
            messagebox.showerror("错误", "请先加载模板图片！")
            return []
        
        # 获取MuMu窗口截图
        if not self.window_manager.find_mumu_window():
            messagebox.showerror("错误", "未找到MuMu模拟器窗口！")
            return []
        
        screenshot = self.window_manager.get_window_screenshot()
        if not screenshot:
            messagebox.showerror("错误", "无法获取窗口截图！")
            return []
        
        # 转换截图为OpenCV格式
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # 执行模板匹配
        result = cv2.matchTemplate(screenshot_cv, self.template_image, method)
        
        # 获取模板尺寸
        template_h, template_w = self.template_image.shape[:2]
        
        # 查找所有满足阈值的匹配
        locations = np.where(result >= threshold)
        matches = []
        
        for pt in zip(*locations[::-1]):  # 切换x和y坐标
            confidence = result[pt[1], pt[0]]
            
            match_info = {
                'confidence': float(confidence),
                'top_left': pt,
                'bottom_right': (pt[0] + template_w, pt[1] + template_h),
                'center': (pt[0] + template_w // 2, pt[1] + template_h // 2),
                'width': template_w,
                'height': template_h
            }
            matches.append(match_info)
        
        # 按置信度排序
        matches.sort(key=lambda x: x['confidence'], reverse=True)
        
        return matches
    
    def click_on_template(self, threshold=0.8, button='left'):
        """在找到的模板位置点击"""
        match_result = self.find_template_in_window(threshold)
        
        if match_result and match_result['found']:
            center_x, center_y = match_result['center']
            
            # 执行点击
            success = self.window_manager.click_at_position(center_x, center_y, button)
            
            if success:
                return {
                    'success': True,
                    'position': (center_x, center_y),
                    'confidence': match_result['confidence']
                }
            else:
                return {'success': False, 'error': '点击失败'}
        else:
            return {
                'success': False, 
                'error': f"未找到模板图片 (置信度: {match_result['confidence']:.2f}, 阈值: {threshold})"
            }
    
    def get_template_info(self):
        """获取当前模板信息"""
        if self.template_image is None:
            return None
        
        height, width = self.template_image.shape[:2]
        return {
            'path': self.template_path,
            'width': width,
            'height': height,
            'channels': self.template_image.shape[2] if len(self.template_image.shape) > 2 else 1
        }
    
    def save_match_result(self, match_info, output_path=None):
        """保存匹配结果图片（在截图上标记匹配位置）"""
        if not match_info or not match_info['found']:
            messagebox.showerror("错误", "没有找到匹配结果！")
            return False
        
        # 获取当前截图
        screenshot = self.window_manager.get_window_screenshot()
        if not screenshot:
            return False
        
        # 转换为OpenCV格式
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # 在匹配位置绘制矩形
        top_left = match_info['top_left']
        bottom_right = match_info['bottom_right']
        center = match_info['center']
        
        # 绘制边框
        cv2.rectangle(screenshot_cv, top_left, bottom_right, (0, 255, 0), 2)
        
        # 绘制中心点
        cv2.circle(screenshot_cv, center, 5, (0, 0, 255), -1)
        
        # 添加置信度文本
        confidence_text = f"Confidence: {match_info['confidence']:.2f}"
        cv2.putText(screenshot_cv, confidence_text, 
                   (top_left[0], top_left[1] - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 保存图片
        if not output_path:
            output_path = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg")]
            )
        
        if output_path:
            # 转换回RGB并保存
            result_rgb = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(result_rgb)
            result_image.save(output_path)
            return True
        
        return False
