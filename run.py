"""
MuMu模拟器自动化工具启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_gui import MuMuAutomationGUI
    
    if __name__ == "__main__":
        print("正在启动MuMu模拟器自动化工具...")
        print("请确保MuMuNxDevice.exe程序正在运行")
        
        app = MuMuAutomationGUI()
        app.run()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖库:")
    print("pip install -r requirements.txt")
    input("按回车键退出...")
except Exception as e:
    print(f"程序启动失败: {e}")
    input("按回车键退出...")
