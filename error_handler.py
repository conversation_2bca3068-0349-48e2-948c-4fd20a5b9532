"""
错误处理和日志模块
"""

import logging
import traceback
import tkinter as tk
from tkinter import messagebox
import os
from datetime import datetime


class ErrorHandler:
    def __init__(self, log_file="mumu_automation.log"):
        self.log_file = log_file
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_error(self, error, context=""):
        """记录错误"""
        error_msg = f"错误: {str(error)}"
        if context:
            error_msg = f"{context} - {error_msg}"
        
        self.logger.error(error_msg)
        self.logger.error(f"详细信息: {traceback.format_exc()}")
    
    def log_info(self, message):
        """记录信息"""
        self.logger.info(message)
    
    def log_warning(self, message):
        """记录警告"""
        self.logger.warning(message)
    
    def handle_screenshot_error(self, error):
        """处理截图错误"""
        self.log_error(error, "截图功能")
        
        error_solutions = {
            "PrintWindow": "尝试使用备用截图方法",
            "BitBlt": "窗口可能被遮挡或最小化",
            "PIL": "屏幕截图权限问题",
            "窗口尺寸": "窗口可能已关闭或最小化"
        }
        
        solution = "未知错误"
        for key, value in error_solutions.items():
            if key in str(error):
                solution = value
                break
        
        return f"截图失败: {solution}"
    
    def handle_window_error(self, error):
        """处理窗口操作错误"""
        self.log_error(error, "窗口操作")
        
        if "未找到" in str(error) or "not found" in str(error).lower():
            return "未找到MuMu窗口，请确保MuMuNxDevice.exe正在运行"
        elif "权限" in str(error) or "access" in str(error).lower():
            return "权限不足，请以管理员身份运行程序"
        else:
            return f"窗口操作失败: {str(error)}"
    
    def handle_image_match_error(self, error):
        """处理图像匹配错误"""
        self.log_error(error, "图像匹配")
        
        if "模板" in str(error) or "template" in str(error).lower():
            return "模板图片加载失败，请检查文件路径和格式"
        elif "匹配" in str(error) or "match" in str(error).lower():
            return "图像匹配失败，请调整阈值或更换模板图片"
        else:
            return f"图像处理失败: {str(error)}"
    
    def show_error_dialog(self, title, message, details=None):
        """显示错误对话框"""
        if details:
            full_message = f"{message}\n\n详细信息:\n{details}"
        else:
            full_message = message
        
        messagebox.showerror(title, full_message)
    
    def show_warning_dialog(self, title, message):
        """显示警告对话框"""
        messagebox.showwarning(title, message)
    
    def show_info_dialog(self, title, message):
        """显示信息对话框"""
        messagebox.showinfo(title, message)


def safe_execute(func, error_handler=None, context="", show_dialog=True):
    """安全执行函数，自动处理异常"""
    try:
        return func()
    except Exception as e:
        if error_handler:
            if "截图" in context:
                message = error_handler.handle_screenshot_error(e)
            elif "窗口" in context:
                message = error_handler.handle_window_error(e)
            elif "图像" in context:
                message = error_handler.handle_image_match_error(e)
            else:
                error_handler.log_error(e, context)
                message = f"{context}失败: {str(e)}"
            
            if show_dialog:
                error_handler.show_error_dialog("操作失败", message)
        else:
            print(f"错误: {e}")
            if show_dialog:
                messagebox.showerror("错误", f"{context}失败: {str(e)}")
        
        return None


# 全局错误处理器实例
global_error_handler = ErrorHandler()


def setup_global_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C中断
            return
        
        global_error_handler.log_error(exc_value, "全局异常")
        
        # 在GUI环境中显示错误
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror(
                "程序错误", 
                f"程序遇到未处理的错误:\n{str(exc_value)}\n\n请查看日志文件获取详细信息。"
            )
            root.destroy()
        except:
            pass
    
    import sys
    sys.excepthook = handle_exception


# 装饰器：自动错误处理
def auto_error_handler(context="", show_dialog=True):
    """装饰器：为函数添加自动错误处理"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return safe_execute(
                lambda: func(*args, **kwargs),
                global_error_handler,
                context,
                show_dialog
            )
        return wrapper
    return decorator
