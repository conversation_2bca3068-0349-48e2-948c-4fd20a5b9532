"""
测试截图功能
"""

from window_manager import MuMuWindowManager

def test_screenshot():
    print("测试MuMu窗口截图功能...")
    
    # 创建窗口管理器
    wm = MuMuWindowManager()
    
    # 查找MuMu窗口
    if wm.find_mumu_window():
        print("✓ 找到MuMu窗口")
        
        # 获取窗口信息
        info = wm.get_window_info()
        if info:
            print(f"窗口标题: {info['title']}")
            print(f"窗口位置: {info['rect']}")
            print(f"窗口可见: {info['is_visible']}")
        
        # 尝试截图
        print("正在尝试截图...")
        screenshot = wm.get_window_screenshot()
        
        if screenshot:
            print("✓ 截图成功！")
            print(f"图片尺寸: {screenshot.size}")
            
            # 保存测试截图
            screenshot.save("test_screenshot.png")
            print("测试截图已保存为 test_screenshot.png")
        else:
            print("✗ 截图失败")
    else:
        print("✗ 未找到MuMu窗口")
        print("请确保MuMuNxDevice.exe程序正在运行")

if __name__ == "__main__":
    test_screenshot()
    input("按回车键退出...")
