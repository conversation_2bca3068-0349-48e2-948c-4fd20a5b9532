"""
抓点抓图工具模块
实现类似按键精灵的抓点抓图功能
"""

import tkinter as tk
from tkinter import messagebox, filedialog
from PIL import Image, ImageTk
import cv2
import numpy as np
from window_manager import MuMuWindowManager
from error_handler import global_error_handler, safe_execute
import os


class CaptureTool:
    def __init__(self, parent=None):
        self.parent = parent
        self.window_manager = MuMuWindowManager()
        self.current_screenshot = None
        self.capture_window = None
        self.canvas = None
        self.photo = None
        self.selected_area = None
        self.start_x = None
        self.start_y = None
        self.rect_id = None
        
    def start_capture(self):
        """开始抓点抓图"""
        def capture_logic():
            # 查找MuMu窗口
            if not self.window_manager.find_mumu_window():
                raise Exception("未找到MuMu模拟器窗口，请确保MuMuNxDevice.exe正在运行")

            # 获取截图
            screenshot = self.window_manager.get_window_screenshot()
            if not screenshot:
                raise Exception("无法获取MuMu窗口截图")

            self.current_screenshot = screenshot
            self.show_capture_window()
            global_error_handler.log_info("抓点抓图工具启动成功")

        safe_execute(capture_logic, global_error_handler, "抓点抓图")
    
    def show_capture_window(self):
        """显示抓图窗口"""
        if self.capture_window:
            self.capture_window.destroy()
        
        self.capture_window = tk.Toplevel(self.parent)
        self.capture_window.title("抓点抓图工具 - MuMu模拟器")
        self.capture_window.geometry("800x600")
        
        # 创建工具栏
        toolbar = tk.Frame(self.capture_window)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        tk.Button(toolbar, text="重新截图", command=self.refresh_screenshot).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="保存截图", command=self.save_screenshot).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="保存选区", command=self.save_selected_area).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="获取坐标", command=self.get_coordinates).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="关闭", command=self.capture_window.destroy).pack(side=tk.RIGHT, padx=2)
        
        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 点击获取坐标，拖拽选择区域")
        status_bar = tk.Label(self.capture_window, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建滚动画布
        canvas_frame = tk.Frame(self.capture_window)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL)
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL)
        
        self.canvas = tk.Canvas(canvas_frame, 
                               yscrollcommand=v_scrollbar.set,
                               xscrollcommand=h_scrollbar.set)
        
        v_scrollbar.config(command=self.canvas.yview)
        h_scrollbar.config(command=self.canvas.xview)
        
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 显示截图
        self.display_screenshot()
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        self.canvas.bind("<Motion>", self.on_canvas_motion)
    
    def display_screenshot(self):
        """在画布上显示截图"""
        if not self.current_screenshot:
            return
        
        # 转换为tkinter可用的格式
        self.photo = ImageTk.PhotoImage(self.current_screenshot)
        
        # 设置画布大小
        self.canvas.config(scrollregion=(0, 0, self.photo.width(), self.photo.height()))
        
        # 显示图片
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
    
    def on_canvas_click(self, event):
        """画布点击事件"""
        # 获取画布坐标
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        self.start_x = canvas_x
        self.start_y = canvas_y
        
        # 清除之前的选择框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
            self.rect_id = None
        
        self.status_var.set(f"起始坐标: ({int(canvas_x)}, {int(canvas_y)})")
    
    def on_canvas_drag(self, event):
        """画布拖拽事件"""
        if self.start_x is None or self.start_y is None:
            return
        
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # 删除之前的矩形
        if self.rect_id:
            self.canvas.delete(self.rect_id)
        
        # 绘制新的选择矩形
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, canvas_x, canvas_y,
            outline="red", width=2
        )
        
        width = abs(canvas_x - self.start_x)
        height = abs(canvas_y - self.start_y)
        self.status_var.set(f"选区: ({int(self.start_x)}, {int(self.start_y)}) 到 ({int(canvas_x)}, {int(canvas_y)}) 大小: {int(width)}x{int(height)}")
    
    def on_canvas_release(self, event):
        """画布释放事件"""
        if self.start_x is None or self.start_y is None:
            return
        
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # 保存选中区域
        self.selected_area = {
            'x1': min(self.start_x, canvas_x),
            'y1': min(self.start_y, canvas_y),
            'x2': max(self.start_x, canvas_x),
            'y2': max(self.start_y, canvas_y)
        }
    
    def on_canvas_motion(self, event):
        """鼠标移动事件"""
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        if self.start_x is None:
            self.status_var.set(f"鼠标位置: ({int(canvas_x)}, {int(canvas_y)})")
    
    def refresh_screenshot(self):
        """重新截图"""
        screenshot = self.window_manager.get_window_screenshot()
        if screenshot:
            self.current_screenshot = screenshot
            self.display_screenshot()
            self.status_var.set("截图已更新")
        else:
            messagebox.showerror("错误", "无法获取新的截图！")
    
    def save_screenshot(self):
        """保存完整截图"""
        if not self.current_screenshot:
            messagebox.showerror("错误", "没有可保存的截图！")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )
        
        if filename:
            self.current_screenshot.save(filename)
            messagebox.showinfo("成功", f"截图已保存到: {filename}")
    
    def save_selected_area(self):
        """保存选中区域"""
        if not self.current_screenshot or not self.selected_area:
            messagebox.showerror("错误", "请先选择一个区域！")
            return
        
        # 裁剪选中区域
        x1, y1, x2, y2 = (int(self.selected_area['x1']), int(self.selected_area['y1']),
                          int(self.selected_area['x2']), int(self.selected_area['y2']))
        
        cropped = self.current_screenshot.crop((x1, y1, x2, y2))
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )
        
        if filename:
            cropped.save(filename)
            messagebox.showinfo("成功", f"选区已保存到: {filename}\n坐标范围: ({x1}, {y1}) 到 ({x2}, {y2})")
    
    def get_coordinates(self):
        """获取坐标信息"""
        if not self.selected_area:
            messagebox.showinfo("坐标信息", "请先选择一个区域或点击一个位置！")
            return
        
        x1, y1, x2, y2 = (int(self.selected_area['x1']), int(self.selected_area['y1']),
                          int(self.selected_area['x2']), int(self.selected_area['y2']))
        
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        info = f"""坐标信息:
左上角: ({x1}, {y1})
右下角: ({x2}, {y2})
中心点: ({center_x}, {center_y})
区域大小: {x2-x1} x {y2-y1}"""
        
        messagebox.showinfo("坐标信息", info)
